import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { DebugElement } from '@angular/core';

import { SwuiDateTimeChooserComponent } from './swui-date-time-chooser.component';
import { DATE_TIME_CHOOSER_MODULES } from './swui-date-time-chooser.module';
import * as moment from 'moment';

function createFakeEvent( type: string ) {
  const event = document.createEvent('Event');
  event.initEvent(type, true, true);
  return event;
}

function dispatchFakeEvent( node: Node | Window, type: string ) {
  node.dispatchEvent(createFakeEvent(type));
}

describe('SwuiDateTimeChooserComponent', () => {
  let component: SwuiDateTimeChooserComponent;
  let fixture: ComponentFixture<SwuiDateTimeChooserComponent>;
  let host: DebugElement;
  let testIsoString: string;
  let testTimeZone: string;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SwuiDateTimeChooserComponent],
      imports: [
        BrowserAnimationsModule,
        ...DATE_TIME_CHOOSER_MODULES
      ]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiDateTimeChooserComponent);
    component = fixture.componentInstance;
    host = fixture.debugElement;
    testIsoString = '2020-06-30T01:01:01.000Z';
    testTimeZone = 'Pacific/Tongatapu';
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set tabindex', () => {
    expect(host.nativeElement.getAttribute('tabindex')).toBe('0');
  });

  it('should disable calendar', () => {
    expect(component.isDisabled).toBeFalsy();
    expect(component.form.disabled).toBeFalsy();

    component.setDisabledState(true);
    expect(component.isDisabled).toBeTruthy();
    expect(component.form.disabled).toBeTruthy();

    component.setDisabledState(false);
    expect(component.isDisabled).toBeFalsy();
    expect(component.form.disabled).toBeFalsy();
  });

  it('should call onTouched on blur', () => {
    spyOn(component, 'onTouched');
    dispatchFakeEvent(host.nativeElement, 'blur');
    expect(component.onTouched).toHaveBeenCalled();
  });

  it('should set onChange in registerOnChange', () => {
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnChange(fn);
    component.dateControl.patchValue(testIsoString);
    expect(test).toBe(true);
  });

  it('should set onChange in registerOnTouched', () => {
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnTouched(fn);
    dispatchFakeEvent(host.nativeElement, 'blur');
    expect(test).toBe(true);
  });

  it('should set value', () => {
    component.value = testIsoString;
    expect(component.value).toBe(testIsoString);

    component.value = 'wrong_string';
    expect(component.value).toBe('');
  });

  it('should writeValue', () => {
    component.writeValue(testIsoString);
    expect(component.value).toBe(testIsoString);

    component.writeValue('wrong_string');
    expect(component.value).toBe('');
  });

  it('should set timeZone', () => {
    component.timeZone = testTimeZone;
    expect(component.timeZone).toBe(testTimeZone);
  });

  it('should set minDate', () => {
    const testDate = moment().clone().add(-1, 'day');
    component.minDate = testDate.clone().toISOString();

    expect(component.minDate).toEqual(testDate.toISOString());
  });

  it('should set maxDate', () => {
    const testDate = moment().clone().add(-1, 'day');
    component.maxDate = testDate.clone().toISOString();

    expect(component.maxDate).toEqual(testDate.toISOString());
  });

  it('should set timepicker', () => {
    component.timePicker = true;
    expect(component.timeControl.disabled).toBe(false);

    component.timePicker = false;
    expect(component.timeControl.disabled).toBe(true);
  });

  it('should patch form', () => {
    spyOn(component, 'onChange');
    component.timeZone = testTimeZone; // +13h
    component.value = testIsoString;
    component.timePicker = true;

    const expected = {
      date: testIsoString,
      time: {
        hour: 14,
        minute: 1,
        second: 1
      }
    };
    expect(component.form.value).toEqual(expected);
  });
});
