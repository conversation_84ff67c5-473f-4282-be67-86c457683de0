import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MatMenuTrigger } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';
import * as moment from 'moment';
import { NEVER } from 'rxjs';

import { CustomPeriodComponent } from './custom-period.component';
import { CustomPeriod } from './custom-period.interface';
import { MODULES } from './custom-period.module';
import { SwuiTopFilterDataService } from '../../swui-schema-top-filter/top-filter-data.service';

function createFakeEvent( type: string ) {
  const event = document.createEvent('Event');
  event.initEvent(type, true, true);
  return event;
}

describe('CustomPeriodComponent', () => {
  let component: CustomPeriodComponent;
  let fixture: ComponentFixture<CustomPeriodComponent>;
  let testDateRange: any;
  let testDate: moment.Moment;
  let customPeriod: CustomPeriod;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        TranslateModule.forRoot(),
        ...MODULES,
      ],
      providers: [
        {
          provide: SwuiTopFilterDataService,
          useValue: {
            onReset: NEVER
          }
        }
      ],
      declarations: [CustomPeriodComponent],
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CustomPeriodComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    testDate = moment.utc().clone();
    testDateRange = { from: testDate.add(-1, 'd'), to: testDate.add(1, 'd') };
    customPeriod = {
      title: 'Test period',
      fn: () => {
        return testDateRange;
      }
    };
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should close menu', () => {
    component.onClick(createFakeEvent('click'), customPeriod);
    const menuTrigger = component.trigger as MatMenuTrigger;
    expect(menuTrigger.menuOpen).toBe(false);
  });

  it('should set current period', () => {
    component.onClick(createFakeEvent('click'), customPeriod);
    expect(component.currentPeriod).toEqual(customPeriod);
  });

  it('should emit current period', () => {
    spyOn(component.periodChange, 'emit');
    component.onClick(createFakeEvent('click'), customPeriod);
    expect(component.periodChange.emit).toHaveBeenCalled();
  });

  it('should reset current period', () => {
    component.currentPeriod = customPeriod;
    component.reset();
    expect(component.currentPeriod).toBeUndefined();
  });

});
